<!DOCTYPE html>
<html>
<head>
    <title>Favicon Generator</title>
</head>
<body>
    <canvas id="canvas" width="32" height="32"></canvas>
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // Background circle
        ctx.fillStyle = '#6366f1';
        ctx.beginPath();
        ctx.arc(16, 16, 15, 0, 2 * Math.PI);
        ctx.fill();
        
        // Border
        ctx.strokeStyle = '#4f46e5';
        ctx.lineWidth = 2;
        ctx.stroke();
        
        // Question mark
        ctx.fillStyle = 'white';
        ctx.font = 'bold 18px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('?', 16, 16);
        
        // Small dots for quiz theme
        ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
        ctx.fillRect(8, 6, 2, 2);
        ctx.fillRect(22, 6, 2, 2);
        ctx.fillRect(6, 24, 2, 2);
        ctx.fillRect(24, 24, 2, 2);
        
        // Convert to ICO (this is just for reference - you'd need to save this manually)
        console.log('Canvas ready - right-click and save as favicon.ico');
    </script>
</body>
</html>
