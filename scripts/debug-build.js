#!/usr/bin/env node

/**
 * Build Debugging Script for Netlify Deployment
 * 
 * This script helps diagnose build issues by checking:
 * - Node.js and npm versions
 * - Package.json configuration
 * - Dependencies installation
 * - Build process
 * - Output verification
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(command, description) {
  try {
    log(`\n🔍 ${description}`, 'blue');
    const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    log(`✅ Success: ${output.trim()}`, 'green');
    return { success: true, output: output.trim() };
  } catch (error) {
    log(`❌ Failed: ${error.message}`, 'red');
    if (error.stdout) log(`stdout: ${error.stdout}`, 'yellow');
    if (error.stderr) log(`stderr: ${error.stderr}`, 'yellow');
    return { success: false, error: error.message };
  }
}

function checkFile(filePath, description) {
  if (fs.existsSync(filePath)) {
    log(`✅ ${description}`, 'green');
    return true;
  } else {
    log(`❌ ${description}`, 'red');
    return false;
  }
}

function analyzePackageJson() {
  log('\n📦 Analyzing package.json', 'blue');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    // Check essential fields
    const requiredFields = ['name', 'scripts', 'dependencies'];
    requiredFields.forEach(field => {
      if (packageJson[field]) {
        log(`✅ Has ${field}`, 'green');
      } else {
        log(`❌ Missing ${field}`, 'red');
      }
    });
    
    // Check build script
    if (packageJson.scripts && packageJson.scripts.build) {
      log(`✅ Build script: ${packageJson.scripts.build}`, 'green');
    } else {
      log('❌ Missing build script', 'red');
    }
    
    // Check Vue dependencies
    const vueDeps = ['vue', 'vue-router', 'vite'];
    vueDeps.forEach(dep => {
      if (packageJson.dependencies?.[dep] || packageJson.devDependencies?.[dep]) {
        log(`✅ Has ${dep}`, 'green');
      } else {
        log(`❌ Missing ${dep}`, 'red');
      }
    });
    
    return true;
  } catch (error) {
    log(`❌ Error reading package.json: ${error.message}`, 'red');
    return false;
  }
}

function checkNetlifyConfig() {
  log('\n⚙️ Checking Netlify configuration', 'blue');
  
  // Check netlify.toml
  if (checkFile('netlify.toml', 'netlify.toml exists')) {
    try {
      const content = fs.readFileSync('netlify.toml', 'utf8');
      
      // Check for common issues
      if (content.includes('command = "npm run build"')) {
        log('✅ Build command configured', 'green');
      } else {
        log('❌ Build command not found or incorrect', 'red');
      }
      
      if (content.includes('publish = "dist"')) {
        log('✅ Publish directory configured', 'green');
      } else {
        log('❌ Publish directory not configured', 'red');
      }
      
      if (content.includes('/*') && content.includes('/index.html')) {
        log('✅ SPA redirect rule found', 'green');
      } else {
        log('❌ SPA redirect rule missing', 'red');
      }
      
      // Check for problematic configurations
      if (content.includes('conditions') || content.includes('Role') || content.includes('Country')) {
        log('⚠️  Warning: Found potentially problematic redirect conditions', 'yellow');
      }
      
      if (content.includes('plugin')) {
        log('⚠️  Warning: Found plugin configuration (may cause build issues)', 'yellow');
      }
      
    } catch (error) {
      log(`❌ Error reading netlify.toml: ${error.message}`, 'red');
    }
  }
  
  // Check _redirects file
  checkFile('public/_redirects', '_redirects file exists in public/');
}

function simulateBuild() {
  log('\n🏗️ Simulating build process', 'blue');
  
  // Clean previous build
  if (fs.existsSync('dist')) {
    log('🧹 Cleaning previous build', 'yellow');
    try {
      fs.rmSync('dist', { recursive: true, force: true });
      log('✅ Previous build cleaned', 'green');
    } catch (error) {
      log(`⚠️  Warning: Could not clean previous build: ${error.message}`, 'yellow');
    }
  }
  
  // Install dependencies
  const installResult = runCommand('npm ci', 'Installing dependencies (npm ci)');
  if (!installResult.success) {
    log('Trying npm install as fallback...', 'yellow');
    runCommand('npm install', 'Installing dependencies (npm install)');
  }
  
  // Run build
  const buildResult = runCommand('npm run build', 'Running build command');
  
  if (buildResult.success) {
    // Check build output
    log('\n📁 Checking build output', 'blue');
    checkFile('dist/index.html', 'index.html in dist/');
    checkFile('dist/_redirects', '_redirects in dist/');
    
    // Check assets
    if (fs.existsSync('dist/assets')) {
      const assets = fs.readdirSync('dist/assets');
      log(`✅ Found ${assets.length} asset files`, 'green');
    } else {
      log('❌ No assets directory found', 'red');
    }
  }
  
  return buildResult.success;
}

function main() {
  log(`${colors.bold}🔧 Netlify Build Debugging Tool${colors.reset}`, 'blue');
  log('This tool will help diagnose build issues for your Vue.js app\n');
  
  // Check environment
  runCommand('node --version', 'Checking Node.js version');
  runCommand('npm --version', 'Checking npm version');
  
  // Analyze configuration
  analyzePackageJson();
  checkNetlifyConfig();
  
  // Test build
  const buildSuccess = simulateBuild();
  
  // Summary and recommendations
  log(`\n${colors.bold}📋 Summary and Recommendations${colors.reset}`, 'blue');
  
  if (buildSuccess) {
    log('🎉 Build completed successfully!', 'green');
    log('\nRecommendations for Netlify:', 'blue');
    log('1. Use Node.js 18 or later', 'yellow');
    log('2. Build command: npm run build', 'yellow');
    log('3. Publish directory: dist', 'yellow');
    log('4. If issues persist, try the minimal netlify.toml', 'yellow');
  } else {
    log('❌ Build failed. Common solutions:', 'red');
    log('1. Check Node.js version compatibility', 'yellow');
    log('2. Verify all dependencies are in package.json', 'yellow');
    log('3. Try deleting node_modules and package-lock.json', 'yellow');
    log('4. Use minimal netlify.toml configuration', 'yellow');
    log('5. Check for syntax errors in configuration files', 'yellow');
  }
  
  log('\nFor Netlify deployment:', 'blue');
  log('- Copy netlify-minimal.toml to netlify.toml if issues persist', 'yellow');
  log('- Set NODE_VERSION environment variable to 18', 'yellow');
  log('- Enable "Skip builds for non-production branches" if needed', 'yellow');
}

main();
