#!/usr/bin/env node

/**
 * Pre-deployment Check Script
 * 
 * Verifies that everything is ready for successful Netlify deployment
 */

import { execSync } from 'child_process';
import fs from 'fs';

const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkNetlifyConfig() {
  log('\n🔍 Checking Netlify Configuration', 'blue');
  
  if (!fs.existsSync('netlify.toml')) {
    log('❌ netlify.toml not found', 'red');
    return false;
  }
  
  const content = fs.readFileSync('netlify.toml', 'utf8');
  
  // Check for problematic configurations that cause exit code 127
  const issues = [];
  
  if (content.includes('conditions')) {
    issues.push('Found redirect conditions (may cause issues)');
  }
  
  if (content.includes('plugin')) {
    issues.push('Found plugin configuration (may cause build failures)');
  }
  
  if (content.includes('Role') || content.includes('Country')) {
    issues.push('Found Role/Country conditions (not suitable for public apps)');
  }
  
  if (!content.includes('command = "npm run build"')) {
    issues.push('Build command not found or incorrect');
  }
  
  if (!content.includes('publish = "dist"')) {
    issues.push('Publish directory not configured correctly');
  }
  
  if (issues.length > 0) {
    log('⚠️  Configuration Issues Found:', 'yellow');
    issues.forEach(issue => log(`   - ${issue}`, 'yellow'));
    return false;
  }
  
  log('✅ Netlify configuration looks good', 'green');
  return true;
}

function testBuild() {
  log('\n🏗️ Testing Build Process', 'blue');
  
  try {
    // Clean build
    if (fs.existsSync('dist')) {
      fs.rmSync('dist', { recursive: true, force: true });
    }
    
    // Test build
    execSync('npm run build', { stdio: 'pipe' });
    
    // Check output
    if (!fs.existsSync('dist/index.html')) {
      log('❌ Build succeeded but index.html not found', 'red');
      return false;
    }
    
    if (!fs.existsSync('dist/_redirects')) {
      log('❌ _redirects file not copied to dist/', 'red');
      return false;
    }
    
    log('✅ Build test successful', 'green');
    return true;
    
  } catch (error) {
    log(`❌ Build failed: ${error.message}`, 'red');
    return false;
  }
}

function main() {
  log(`${colors.bold}🚀 Pre-Deployment Check${colors.reset}`, 'blue');
  log('Verifying readiness for Netlify deployment\n');
  
  let allGood = true;
  
  // Check configuration
  allGood &= checkNetlifyConfig();
  
  // Test build
  allGood &= testBuild();
  
  // Final verdict
  log(`\n${colors.bold}📋 Deployment Readiness${colors.reset}`, 'blue');
  
  if (allGood) {
    log('🎉 All checks passed! Ready for deployment.', 'green');
    log('\nDeployment steps:', 'blue');
    log('1. git add . && git commit -m "Fix Netlify configuration"', 'yellow');
    log('2. git push origin main', 'yellow');
    log('3. Deploy on Netlify with these settings:', 'yellow');
    log('   - Build command: npm run build', 'yellow');
    log('   - Publish directory: dist', 'yellow');
    log('   - Node version: 18 (in environment variables)', 'yellow');
  } else {
    log('❌ Issues found. Please fix before deploying.', 'red');
    log('\nQuick fixes:', 'blue');
    log('1. Use minimal netlify.toml configuration', 'yellow');
    log('2. Remove problematic redirect conditions', 'yellow');
    log('3. Ensure build works locally', 'yellow');
    process.exit(1);
  }
}

main();
