# Netlify Build Troubleshooting Guide

## 🚨 Exit Code 127 - Command Not Found

Exit code 127 typically indicates one of these issues:

### 1. **Node.js/npm Version Issues**
- Netlify can't find the `npm` command
- Wrong Node.js version specified
- npm version incompatibility

### 2. **Configuration Syntax Errors**
- Invalid `netlify.toml` syntax
- Problematic redirect conditions
- Plugin configuration issues

### 3. **Missing Dependencies**
- Dependencies not properly specified in `package.json`
- Lock file conflicts
- Build tool not found

## 🔧 Step-by-Step Fix

### Step 1: Use Minimal Configuration

Replace your current `netlify.toml` with this minimal version:

```toml
[build]
  command = "npm run build"
  publish = "dist"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

### Step 2: Set Environment Variables in Netlify

In your Netlify dashboard:
1. Go to Site settings → Environment variables
2. Add: `NODE_VERSION` = `18`
3. Add: `NPM_VERSION` = `9` (optional)

### Step 3: Verify Build Settings

In Netlify dashboard → Site settings → Build & deploy:
- **Build command**: `npm run build`
- **Publish directory**: `dist`
- **Base directory**: (leave empty)

### Step 4: Clear Build Cache

In Netlify dashboard:
1. Go to Site settings → Build & deploy
2. Click "Clear cache and retry deploy"

## 🧪 Local Testing

Run the debugging script to identify issues:

```bash
node scripts/debug-build.js
```

This will check:
- Node.js and npm versions
- Package.json configuration
- Dependencies
- Build process
- Output verification

## 🔍 Common Issues and Solutions

### Issue 1: Invalid Redirect Conditions

**Problem**: 
```toml
conditions = {Role = ["admin"], Country = ["US"]}
```

**Solution**: Remove conditions entirely for public apps
```toml
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

### Issue 2: Plugin Configuration Errors

**Problem**: 
```toml
[[plugins]]
  package = "@netlify/plugin-lighthouse"
```

**Solution**: Remove plugin configuration or install the plugin
```bash
npm install --save-dev @netlify/plugin-lighthouse
```

### Issue 3: Multiple Conflicting Redirects

**Problem**: Multiple redirect rules that conflict

**Solution**: Use single, simple redirect rule
```toml
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  force = false
```

### Issue 4: Node.js Version Mismatch

**Problem**: Using unsupported Node.js version

**Solution**: Set specific version in netlify.toml
```toml
[build.environment]
  NODE_VERSION = "18"
```

## 🚀 Deployment Checklist

Before deploying:

- [ ] `package.json` has all required dependencies
- [ ] `npm run build` works locally
- [ ] `dist` folder contains `index.html` and `_redirects`
- [ ] `netlify.toml` uses minimal configuration
- [ ] Node.js version is 16 or higher
- [ ] No syntax errors in configuration files

## 🔄 Alternative Deployment Methods

### Method 1: Manual Deploy (for testing)

1. Run `npm run build` locally
2. Drag and drop `dist` folder to Netlify
3. Test if the issue is configuration-related

### Method 2: Use _redirects Only

1. Remove `netlify.toml` entirely
2. Rely only on `public/_redirects` file:
   ```
   /*    /index.html   200
   ```

### Method 3: Minimal netlify.toml

Use the provided `netlify-minimal.toml`:
```bash
cp netlify-minimal.toml netlify.toml
```

## 📊 Build Log Analysis

Look for these patterns in Netlify build logs:

### Success Indicators:
```
✓ Installing dependencies
✓ Build script succeeded
✓ Site is live
```

### Failure Indicators:
```
✗ Command failed with exit code 127
✗ npm: command not found
✗ Build script returned non-zero exit code
```

## 🛠️ Advanced Debugging

### Enable Debug Mode

Add to `netlify.toml`:
```toml
[build.environment]
  NODE_ENV = "production"
  DEBUG = "*"
```

### Check Build Image

Netlify uses Ubuntu-based build images. Ensure compatibility:
- Node.js 16+ recommended
- npm 8+ recommended
- Standard Unix tools available

### Custom Build Command

If standard build fails, try:
```toml
[build]
  command = "npm ci && npm run build"
```

## 📞 Getting Help

If issues persist:

1. **Check Netlify Status**: https://www.netlifystatus.com/
2. **Community Forum**: https://community.netlify.com/
3. **Build Logs**: Download full logs from Netlify dashboard
4. **Local Reproduction**: Ensure issue reproduces locally

## 🎯 Quick Fix Summary

For immediate resolution:

1. **Replace netlify.toml** with minimal version
2. **Set NODE_VERSION=18** in environment variables
3. **Clear build cache** and redeploy
4. **Test locally** with `npm run build`
5. **Use manual deploy** if automated fails

This should resolve the exit code 127 error and get your Vue.js quiz app deployed successfully!
